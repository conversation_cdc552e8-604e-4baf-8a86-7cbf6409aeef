1.博克高定订单下发接口

    1.1 请求URL
    http://101.132.244.100:31201/out/pp/create/model3/3dbe5179-bd09-4999-a505-d1399e22b8c1

    1.2 请求方法：POST

    1.3 请求头：Content-Type: application/json

    1.4 请求参数：
        1.4.1 Path 参数
        | 参数名称  | 参数说明 | 是否必须 | 数据类型   |
        | ----- | ---- | ---- | ------ |
        | token | 请求令牌 | 是    | string |

        1.4.2 Query 参数
        | 参数名称   | 参数说明                 | 是否必须 | 数据类型   |
        | ------ | -------------------- | ---- | ------ |
        | status | 订单状态(新单\|审核通过\|产前完成) | 否    | string |

        1.4.3 Body 参数（JSON）
        根对象：modelRequest
        | 参数名称            | 参数说明         | 是否必须 | 数据类型    |
        | --------------- | ------------ | ---- | ------- |
        | orderPno        | 生产计划号        | 是    | string  |
        | ppFrom          | 订单来源系统       | 是    | string  |
        | thirdUid        | 订单唯一主键(来源系统) | 是    | string  |
        | dueDate         | 交货日期         | 是    | string  |
        | lineList        | 订单明细         | 是    | array   |
        | customerId      | 客户编号         | 否    | string  |
        | customerName    | 客户名称         | 否    | string  |
        | deliveryAddress | 发货地址         | 否    | string  |
        | deliveryCountry | 发货国家         | 否    | string  |
        | deliveryTel     | 发货联系         | 否    | string  |
        | devliveryWay    | 发货方式         | 否    | string  |
        | mrchds          | 业务员          | 否    | string  |
        | orderDate       | 下单日期         | 否    | string  |
        | orderNo         | 销售订单号        | 否    | string  |
        | orderTotal      | 订单数量         | 否    | integer |
        | remark          | 订单备注         | 否    | string  |
        | wkspcd          | 指定车间         | 否    | string  |

            ******* lineList（订单明细）
            | 参数名称     | 参数说明     | 是否必须 | 数据类型   |
            | -------- | -------- | ---- | ------ |
            | mainMid  | 主料(面料)编号 | 是    | string |
            | prodId   | 品类编号     | 是    | string |
            | stylId   | 款式编号     | 是    | string |
            | itemList | 尺码清单     | 是    | array  |
            | bomList  | 物料清单     | 否    | array  |
            | tecList  | 工艺清单     | 否    | array  |

            1.4.3.2 itemList（尺码清单）
            | 参数名称    | 参数说明         | 是否必须 | 数据类型    |
            | ------- | ------------ | ---- | ------- |
            | itemQty | 数量           | 是    | integer |
            | sizeNo  | 尺码           | 是    | string  |
            | cusadr  | 消费者地址        | 否    | string  |
            | cushgt  | 消费者身高        | 否    | string  |
            | cusicd  | 消费者加款唯一ID    | 否    | string  |
            | cusnam  | 消费者名称        | 否    | string  |
            | cusncd  | 消费者编号        | 否    | string  |
            | cussex  | 消费者性别(1男，0女) | 否    | string  |
            | custel  | 消费者电话        | 否    | string  |
            | cuswgt  | 消费者体重        | 否    | string  |
            | deptnm  | 消费者部门        | 否    | string  |
            | msutnm  | 量体师名称        | 否    | string  |
            | surList | 量体明细         | 否    | array   |

            1.4.3.3 surList（量体明细）
            | 参数名称    | 参数说明 | 是否必须 | 数据类型   |
            | ------- | ---- | ---- | ------ |
            | msuCode | 量体编码 | 是    | string |
            | msuType | 量体方式 | 是    | string |
            | msuVal  | 量体结果 | 是    | string |

            1.4.3.4 bomList（物料清单）
            | 参数名称             | 参数说明           | 是否必须 | 数据类型    |
            | ---------------- | -------------- | ---- | ------- |
            | mainMterial      | 主料(非常备)/辅料(常备) | 是    | boolean |
            | materialId       | 物料编号           | 是    | string  |
            | cut\_width       | 裁剪宽度           | 否    | string  |
            | cut\_wstrat      | 裁剪用量           | 否    | string  |
            | materialBatchno  | 物料批次           | 否    | string  |
            | materialClrnm    | 物料颜色名称         | 否    | string  |
            | materialColor    | 物料颜色编号         | 否    | string  |
            | materialCom      | 物料成份           | 否    | string  |
            | materialName     | 物料名称           | 否    | string  |
            | materialTypeId   | 物料分类编号         | 否    | string  |
            | materialTypeName | 物料分类名称         | 否    | string  |
            | materialUsage    | 物料用途           | 否    | string  |
            | thirdUid         | 每三方系统唯一主键      | 否    | string  |
            | tolConsu         | 总用量            | 否    | number  |
            | uniConsu         | 物料单耗           | 否    | number  |
            | unitcd           | 单位编号           | 否    | string  |
            | unitnm           | 单位名称           | 否    | string  |
            | width            | 物料宽度           | 否    | number  |

            1.4.3.5 tecList（工艺清单）
            | 参数名称        | 参数说明      | 是否必须 | 数据类型   |
            | ----------- | --------- | ---- | ------ |
            | craftdetail | 工艺描述      | 否    | string |
            | img1Name    | 工艺图1名称    | 否    | string |
            | img1Url     | 工艺图1下载地址  | 否    | string |
            | img2Name    | 工艺图2名称    | 否    | string |
            | img2Url     | 工艺图2下载地址  | 否    | string |
            | img3Name    | 工艺图3名称    | 否    | string |
            | img3Url     | 工艺图3下载地址  | 否    | string |
            | pdf1Name    | 工艺文件1名称   | 否    | string |
            | pdf1Url     | 工艺文件1下载地址 | 否    | string |
            | pdf2Name    | 工艺文件2名称   | 否    | string |
            | pdf2Url     | 工艺文件2下载地址 | 否    | string |
            | pdf3Name    | 工艺文件3名称   | 否    | string |
            | pdf3Url     | 工艺文件3下载地址 | 否    | string |
            | pdptcd      | 部件编号      | 否    | string |
            | pdptnm      | 部件名称      | 否    | string |
            | wctycd      | 工段编号      | 否    | string |
            | wctynm      | 工段名称      | 否    | string |
            | unitnm      | 单位        | 否    | string |


    1.5请求示例
    {
    "customerId": "CUS001",
    "customerName": "测试客户",
    "deliveryAddress": "上海市浦东新区",
    "deliveryCountry": "中国",
    "deliveryTel": "13800138000",
    "devliveryWay": "快递",
    "dueDate": "2027-04-30",
    "lineList": [
        {
            "bomList": [
                {
                    "cutWidth": "150",
                    "cutWstrat": "2",
                    "mainMterial": true,
                    "materialBatchno": "BATCH001",
                    "materialClrnm": "深蓝色",
                    "materialColor": "NAVY",
                    "materialCom": "100%羊毛",
                    "materialId": "MAT001",
                    "materialName": "羊毛面料",
                    "materialTypeId": "TYPE001",
                    "materialTypeName": "面料",
                    "materialUsage": "西装面料",
                    "thirdUid": "THIRD001",
                    "tolConsu": 3.5,
                    "uniConsu": 1.5,
                    "unitcd": "M",
                    "unitnm": "米",
                    "width": 150.0
                }
            ],
            "finishedSize": "175/92A",
            "itemList": [
                {
                    "cusadr": "上海市浦东新区",
                    "cushgt": "175",
                    "cusicd": "CI001",
                    "cusnam": "张三",
                    "cusncd": "CN001",
                    "cussex": "1",
                    "custel": "13900139000",
                    "cuswgt": "65",
                    "deptnm": "销售部",
                    "itemQty": 1,
                    "msutnm": "王师傅",
                    "sizeNo": "175/92A",
                    "surList": [
                        {
                            "msuCode": "MS001",
                            "msuType": "手工量体",
                            "msuVal": "92"
                        }
                    ]
                }
            ],
            "mainMcolor": "深蓝色",
            "mainMid": "MAT001",
            "mainMname": "羊毛面料",
            "mainSpec": "150cm",
            "prodId": "PROD001",
            "prodName": "西装",
            "progtype": "定制",
            "remark": "要求做工精细",
            "skuName": "商务西装",
            "skuSpec": "标准版型",
            "stylId": "STYLE001",
            "stylName": "商务双排扣",
            "tecList": [
                {
                    "craftdetail": "双排扣工艺说明",
                    "img1Name": "正面图",
                    "img1Url": "http://example.com/img1.jpg",
                    "pdptcd": "PART001",
                    "pdptnm": "前片",
                    "wctycd": "PROC001",
                    "wctynm": "裁剪"
                }
            ],
            "unitnm": "件"
        }
    ],
    "mrchds": "李四",
    "orderDate": "2027-03-20",
    "orderNo": "SO24032001",
    "orderPno": "PP24032001",
    "orderTotal": 1,
    "ppFrom": "MTM系统",
    "remark": "测试订单",
    "thirdUid": "ORDER001",
    "wkspcd": "WS001"
    } 

    1.6响应示例：
    {
        "txt": "",
        "st": "0成功，-1失败",
        "msg": "Success",
        "body": {
            "pplanno": "PP24060078"
        }
    }



2.博克排料结果回传接口

    2.1请求URL
    http://101.132.244.100:31201/boke/backcad/3dbe5179-bd09-4999-a505-d1399e22b8c1

    2.2请求方法：POST

    2.3请求头：Content-Type: application/json;charset=UTF-8

    2.4 请求参数：
        一级字段说明
        | 字段名             | 类型     | 说明      | 备注                       |
        | --------------- | ------ | ------- | ------------------------ |
        | orderId         | string | 订单号     |                          |
        | createdAt       | string | 排料完成时间  | 格式：yyyy-MM-dd HH\:mm\:ss |
        | patternFileUrl  | string | 纸样文件网址  | 团单或混排订单时，为排料图文件          |
        | styleCode       | string | 款式编码    | 多款混排时，款号用`,`分隔           |
        | extra           | string | 附加内容    | 来源于订单的 extra 字段          |
        | stylePatterns   | array  | 团单版型数据  | 团单时有值                    |
        | dosages         | array  | 库存用量表   |                          |
        | materialMarkers | array  | 排料结果    |                          |
        | mcSizes         | array  | 毛裁尺寸表   | 适用于团单                    |
        | fullSizeTechs   | array  | 齐码工艺尺寸表 |                          |
        | technologies    | array  | 生产工艺表   |                          |

        stylePatterns（团单版型数据）
        | 字段名              | 类型     | 说明     |
        | ---------------- | ------ | ------ |
        | styleCode        | string | 款式编码   |
        | stylePatternFile | string | 款式版型文件 |
        | sizes            | array  | 尺码表    |

        sizes（尺码表）
        | 字段名   | 类型     | 说明   |
        | ----- | ------ | ---- |
        | name  | string | 尺码名称 |
        | specs | array  | 尺寸规范 |

        specs（尺寸规范）
        | 字段名   | 类型     | 说明      |
        | ----- | ------ | ------- |
        | name  | string | 尺寸名称    |
        | value | string | 尺寸值（cm） |

        dosages（库存用量表）
        | 字段名    | 类型     | 说明       |
        | ------ | ------ | -------- |
        | code   | string | 库存编码     |
        | dosage | number | 用量（单位：m） |

        materialMarkers（排料结果）
        | 字段名          | 类型     | 说明   |
        | ------------ | ------ | ---- |
        | materialCode | string | 客户编码 |
        | markerTables | array  | 床次信息 |

        markerTables（床次信息）
        | 字段名            | 类型     | 说明                |
        | -------------- | ------ | ----------------- |
        | index          | string | 床次（从1开始）          |
        | no             | string | 床次标识              |
        | plies          | string | 层数                |
        | length         | string | 长度（m）             |
        | stripeLength   | string | 条格宽度长（m）          |
        | width          | string | 幅宽（m）             |
        | realWidth      | string | 排料宽度（m）           |
        | usage          | string | 利用率（如：88.88）      |
        | packages       | string | 件数                |
        | cutPartsCount  | string | 裁片数量              |
        | cutImageUrl    | string | 排料要点 URL          |
        | cutFileUrl     | string | 排料文件 URL          |
        | cutPltFileUrl  | string | 排料 Plt 文件 URL     |
        | cutFile2Url    | string | 排料文件 URL（nc 文件）   |
        | cutPltFile2Url | string | 排料 Plt 文件 URL（可选） |
        | splitPieces    | array  | 分割片名列表            |
        | sizes          | array  | 尺码配比              |

        cutGuides（手术指导书，团单）
        | 字段名        | 类型     | 说明    |
        | ---------- | ------ | ----- |
        | pack       | string | 毛裁包信息 |
        | serialNo   | string | 流水号   |
        | clientNo   | string | 客户编号  |
        | clientName | string | 客户姓名  |
        | size       | string | 尺码    |
        | specs      | array  | 变化量   |

        trimGuides（指导书，团单）
        | 字段名   | 类型     | 说明    |
        | ----- | ------ | ----- |
        | pack  | string | 毛裁包信息 |
        | size  | string | 标准码   |
        | specs | array  | 宽度    |

        directions（尺码方向）
        | 字段名       | 类型     | 说明              |
        | --------- | ------ | --------------- |
        | size      | string | 尺码名称            |
        | count     | number | 数量              |
        | direction | number | 方向（0右，1左，2 180） |

    2.5请求示例
    {
        "orderId": "SO26032001",
        "createdAt": "2026-03-20 15:30:00",
        "patternFileUrl": "http://example.com/pattern.pdf",
        "styleCode": "STYLE001",
        "extra": "测试排料数据",
        "stylePatterns": [
            {
                "styleCode": "STYLE001",
                "stylePatternFile": "http://example.com/style-pattern.pdf"
            }
        ],
        "sizes": [
            {
                "name": "175/92A",
                "specs": [
                    {
                        "name": "胸围",
                        "value": "92"
                    },
                    {
                        "name": "腰围",
                        "value": "76"
                    }
                ]
            }
        ],
        "dosages": [
            {
                "code": "MAT001",
                "dosage": 3.5
            }
        ],
        "materialMarkers": [
            {
                "materialCode": "MAT001",
                "markerTables": [
                    {
                        "index": "1",
                        "no": "B001",
                        "plies": "1",
                        "length": "3.5",
                        "stripeLength": "3.5",
                        "width": "150",
                        "realWidth": "145",
                        "usage": "85.5",
                        "packages": "1",
                        "cutPartsCount": "20",
                        "cutImageUrl": "http://example.com/cut-image.jpg",
                        "cutFileUrl": "http://example.com/cut-file.cut",
                        "cutPltFileUrl": "http://example.com/cut-file.plt",
                        "splitPieces": ["前片", "后片", "袖子"],
                        "sizes": [
                            {
                                "size": "175/92A",
                                "count": "1",
                                "styleCode": "STYLE001",
                                "matchSize": "标准"
                            }
                        ],
                        "pavings": [
                            {
                                "index": "1",
                                "plies": "1",
                                "length": "3.5",
                                "packages": "1"
                            }
                        ],
                        "cutGuides": [
                            {
                                "pack": "包1",
                                "serialNo": "SN001",
                                "clientNo": "CN001",
                                "clientName": "张三",
                                "size": "175/92A",
                                "specs": [
                                    {
                                        "name": "胸围",
                                        "value": "+1"
                                    }
                                ]
                            }
                        ],
                        "trimGuides": [
                            {
                                "pack": "包1",
                                "size": "175/92A",
                                "specs": [
                                    {
                                        "name": "胸围",
                                        "value": "0"
                                    }
                                ],
                                "quantity": "1"
                            }
                        ],
                        "avgLength": 3.5,
                        "perimeter": 450.0,
                        "directions": [
                            {
                                "size": "175/92A",
                                "count": 1,
                                "direction": 0
                            }
                        ]
                    }
                ]
            }
        ]
    }

    2.6响应
    HTTP 状态码:200
    消息体(不区分大小写):SUCCESS

3.博克数据中心接收订单接口

    3.1请求URL
    http://101.132.244.100:31201/boke/backcad/3dbe5179-bd09-4999-a505-d1399e22b8c1

    3.2请求方法：POST

    3.3请求头：
    Content-Type: application/json
    X-Auth-Token: eyJhbGciOiJIUzI1NiJ9.eyJ1Ijp7ImlkIjoiNWMxOWQ4MTEzNjcxZjg1ZDlkYzVhOTQwIiwicm9sZXMiOlsiU0kiXSwidXNlcm5hbWUiOiJzaSIsIm5hbWUiOiJzaSJ9LCJleHAiOjIwNjU3NjEyODJ9.lf4XB4mS5Izhvn817jd9Ht_bHSQ4fLd3NFpBVY8r0Qw

    3.4 请求参数：
        3.4.1 Path 参数
        | 参数名称  | 参数说明 | 是否必须 | 数据类型   |
        | ----- | ---- | ---- | ------ |
        | token | 请求令牌 | 是    | string |

        3.4.2 Query 参数
        | 参数名称   | 参数说明                 | 是否必须 | 数据类型   |
        | ------ | -------------------- | ---- | ------ |
        | status | 订单状态(新单\|审核通过\|产前完成) | 否    | string |

        3.4.3 Body 参数（JSON）
        根对象：modelRequest
        | 参数名称            | 参数说明         | 是否必须 | 数据类型    |
        | --------------- | ------------ | ---- | ------- |
        | orderPno        | 生产计划号        | 是    | string  |
        | ppFrom          | 订单来源系统       | 是    | string  |
        | thirdUid        | 订单唯一主键(来源系统) | 是    | string  |
        | dueDate         | 交货日期         | 是    | string  |
        | lineList        | 订单明细         | 是    | array   |
        | customerId      | 客户编号         | 否    | string  |
        | customerName    | 客户名称         | 否    | string  |
        | deliveryAddress | 发货地址         | 否    | string  |
        | deliveryCountry | 发货国家         | 否    | string  |
        | deliveryTel     | 发货联系         | 否    | string  |
        | devliveryWay    | 发货方式         | 否    | string  |
        | mrchds          | 业务员          | 否    | string  |
        | orderDate       | 下单日期         | 否    | string  |
        | orderNo         | 销售订单号        | 否    | string  |
        | orderTotal      | 订单数量         | 否    | integer |
        | remark          | 订单备注         | 否    | string  |
        | wkspcd          | 指定车间         | 否    | string  |

            ******* lineList（订单明细）
            | 参数名称     | 参数说明     | 是否必须 | 数据类型   |
            | -------- | -------- | ---- | ------ |
            | mainMid  | 主料(面料)编号 | 是    | string |
            | prodId   | 品类编号     | 是    | string |
            | stylId   | 款式编号     | 是    | string |
            | itemList | 尺码清单     | 是    | array  |
            | bomList  | 物料清单     | 否    | array  |
            | tecList  | 工艺清单     | 否    | array  |

            3.4.3.2 itemList（尺码清单）
            | 参数名称    | 参数说明         | 是否必须 | 数据类型    |
            | ------- | ------------ | ---- | ------- |
            | itemQty | 数量           | 是    | integer |
            | sizeNo  | 尺码           | 是    | string  |
            | cusadr  | 消费者地址        | 否    | string  |
            | cushgt  | 消费者身高        | 否    | string  |
            | cusicd  | 消费者加款唯一ID    | 否    | string  |
            | cusnam  | 消费者名称        | 否    | string  |
            | cusncd  | 消费者编号        | 否    | string  |
            | cussex  | 消费者性别(1男，0女) | 否    | string  |
            | custel  | 消费者电话        | 否    | string  |
            | cuswgt  | 消费者体重        | 否    | string  |
            | deptnm  | 消费者部门        | 否    | string  |
            | msutnm  | 量体师名称        | 否    | string  |
            | surList | 量体明细         | 否    | array   |

            3.4.3.3 surList（量体明细）
            | 参数名称    | 参数说明 | 是否必须 | 数据类型   |
            | ------- | ---- | ---- | ------ |
            | msuCode | 量体编码 | 是    | string |
            | msuType | 量体方式 | 是    | string |
            | msuVal  | 量体结果 | 是    | string |

            3.4.3.4 bomList（物料清单）
            | 参数名称             | 参数说明           | 是否必须 | 数据类型    |
            | ---------------- | -------------- | ---- | ------- |
            | mainMterial      | 主料(非常备)/辅料(常备) | 是    | boolean |
            | materialId       | 物料编号           | 是    | string  |
            | cut\_width       | 裁剪宽度           | 否    | string  |
            | cut\_wstrat      | 裁剪用量           | 否    | string  |
            | materialBatchno  | 物料批次           | 否    | string  |
            | materialClrnm    | 物料颜色名称         | 否    | string  |
            | materialColor    | 物料颜色编号         | 否    | string  |
            | materialCom      | 物料成份           | 否    | string  |
            | materialName     | 物料名称           | 否    | string  |
            | materialTypeId   | 物料分类编号         | 否    | string  |
            | materialTypeName | 物料分类名称         | 否    | string  |
            | materialUsage    | 物料用途           | 否    | string  |
            | thirdUid         | 每三方系统唯一主键      | 否    | string  |
            | tolConsu         | 总用量            | 否    | number  |
            | uniConsu         | 物料单耗           | 否    | number  |
            | unitcd           | 单位编号           | 否    | string  |
            | unitnm           | 单位名称           | 否    | string  |
            | width            | 物料宽度           | 否    | number  |

            3.4.3.5 tecList（工艺清单）
            | 参数名称        | 参数说明      | 是否必须 | 数据类型   |
            | ----------- | --------- | ---- | ------ |
            | craftdetail | 工艺描述      | 否    | string |
            | img1Name    | 工艺图1名称    | 否    | string |
            | img1Url     | 工艺图1下载地址  | 否    | string |
            | img2Name    | 工艺图2名称    | 否    | string |
            | img2Url     | 工艺图2下载地址  | 否    | string |
            | img3Name    | 工艺图3名称    | 否    | string |
            | img3Url     | 工艺图3下载地址  | 否    | string |
            | pdf1Name    | 工艺文件1名称   | 否    | string |
            | pdf1Url     | 工艺文件1下载地址 | 否    | string |
            | pdf2Name    | 工艺文件2名称   | 否    | string |
            | pdf2Url     | 工艺文件2下载地址 | 否    | string |
            | pdf3Name    | 工艺文件3名称   | 否    | string |
            | pdf3Url     | 工艺文件3下载地址 | 否    | string |
            | pdptcd      | 部件编号      | 否    | string |
            | pdptnm      | 部件名称      | 否    | string |
            | wctycd      | 工段编号      | 否    | string |
            | wctynm      | 工段名称      | 否    | string |
            | unitnm      | 单位        | 否    | string |
    
    3.5请求示例
    {
    "orderId": "PP2506000105",
    "type": "group",
    "styles": [
        {
        "styleCode": "JMXY-02",
        "materials": null,
        "customUnits": []
        }
    ],
    "quantity": 23,
    "sizesQuantity": [
        {
        "clientNo": "1",
        "clientName": "李嘉",
        "clientSex": null,
        "styleCode": "JMXY-02",
        "size": "XL",
        "quantity": 23,
        "specs": []
        }
    ],
    "materials": [
        {
        "patternMaterialCode": null,
        "code": "DT-34003",
        "name": "DT-34003",
        "width": "1.47",
        "warpWaterShrinkage": null,
        "weftWaterShrinkage": null,
        "warpHeatShrinkage": null,
        "weftHeatShrinkage": null,
        "warpElasticExtension": null,
        "weftElasticExtension": null,
        "directionFlag": null,
        "sideDiffFlag": null,
        "midSideDiffFlag": null,
        "segmentFlag": null,
        "warpStart": null,
        "warpRepeat": null,
        "weftStart": null,
        "weftRepeat": null,
        "cutBedConfig": null,
        "restrictedAreas": null
        }
    ],
    "auditForced": false,
    "extra": "a148c16e-c788-4dec-aaf3-3e609cdc62cd",
    "styleCode": "JMXY-02",
    "materialCode": "QZ-230231"
    }

    3.6响应
    {
        "code": "0成功，-1失败",
        "msg": "Success"
    }